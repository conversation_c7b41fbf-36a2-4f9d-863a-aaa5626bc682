﻿"restore":{"projectUniqueName":"/Users/<USER>/MinimalMcpServer/MinimalMcpServer.csproj","projectName":"MinimalMcpServer","projectPath":"/Users/<USER>/MinimalMcpServer/MinimalMcpServer.csproj","outputPath":"/Users/<USER>/MinimalMcpServer/obj/","projectStyle":"PackageReference","originalTargetFrameworks":["net8.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0":{"targetAlias":"net8.0","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"}}"frameworks":{"net8.0":{"targetAlias":"net8.0","dependencies":{"Microsoft.Extensions.Hosting":{"target":"Package","version":"[9.0.6, )"},"ModelContextProtocol":{"target":"Package","version":"[0.2.0-preview.3, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"/usr/local/share/dotnet/sdk/8.0.411/PortableRuntimeIdentifierGraph.json"}}